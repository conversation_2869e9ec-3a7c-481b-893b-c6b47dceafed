// src/app/page.tsx
'use client';

import SpliteHero from '@/components/SpliteHero';
import { DisplayCardsDemo } from '@/components/ui/display_cards';

export default function Home() {
  return (
    <main className="min-h-screen bg-black flex flex-col items-center py-12 px-4 sm:px-8 gap-12">
      {/* 1) The 500px-tall hero section */}
      <div className="w-full max-w-3xl">
        <SpliteHero />
      </div>

      {/* 2) Cards appear in their own section BELOW the hero */}
      <div className="w-full max-w-3xl">
        <DisplayCardsDemo />
      </div>
    </main>
  );
}