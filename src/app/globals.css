@import "tailwindcss";

:root {
  --background: #000000;
  --foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #000000;
    --foreground: #ffffff;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), Arial, Helvetica, sans-serif;
  overflow-x: hidden;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #ef4444, #dc2626);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #f87171, #ef4444);
}

/* Dopamine-triggering animations */
@keyframes glow {
  0%, 100% { box-shadow: 0 0 20px rgba(239, 68, 68, 0.3); }
  50% { box-shadow: 0 0 40px rgba(239, 68, 68, 0.6); }
}

@keyframes pulse-red {
  0%, 100% { background-color: rgba(239, 68, 68, 0.1); }
  50% { background-color: rgba(239, 68, 68, 0.3); }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0); }
  50% { opacity: 1; transform: scale(1); }
}

/* Utility classes */
.glow-red {
  animation: glow 2s ease-in-out infinite;
}

.pulse-bg-red {
  animation: pulse-red 2s ease-in-out infinite;
}

.float {
  animation: float 3s ease-in-out infinite;
}

.sparkle {
  animation: sparkle 1.5s ease-in-out infinite;
}

/* Selection styling */
::selection {
  background: rgba(239, 68, 68, 0.3);
  color: white;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible {
  outline: 2px solid #ef4444;
  outline-offset: 2px;
}
