'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { Calendar, Award, Rocket, Users, Lightbulb, Target } from 'lucide-react';

const Timeline = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const milestones = [
    {
      year: "2019",
      title: "Company Founded",
      description: "SATINAV Robotics was established with a vision to revolutionize ground robotics technology.",
      icon: Lightbulb,
      color: "from-blue-500 to-cyan-400",
      achievements: ["Initial funding secured", "Core team assembled", "First prototype developed"]
    },
    {
      year: "2020",
      title: "First Prototype",
      description: "Successfully developed and tested our first modular ground robot prototype.",
      icon: Rocket,
      color: "from-green-500 to-emerald-400",
      achievements: ["Prototype testing completed", "Patent applications filed", "Technology validation"]
    },
    {
      year: "2021",
      title: "Series A Funding",
      description: "Raised $25M in Series A funding to accelerate development and expand the team.",
      icon: Award,
      color: "from-purple-500 to-pink-400",
      achievements: ["$25M funding raised", "Team expanded to 50+", "R&D facility established"]
    },
    {
      year: "2022",
      title: "Military Contract",
      description: "Secured first major military contract for reconnaissance robot deployment.",
      icon: Target,
      color: "from-red-500 to-orange-400",
      achievements: ["Military partnership", "Field testing success", "Production scaling"]
    },
    {
      year: "2023",
      title: "Global Expansion",
      description: "Expanded operations internationally with offices in Europe and Asia.",
      icon: Users,
      color: "from-indigo-500 to-blue-400",
      achievements: ["International offices", "Global partnerships", "200+ employees"]
    },
    {
      year: "2024",
      title: "AI Integration",
      description: "Launched next-generation robots with advanced AI and autonomous capabilities.",
      icon: Calendar,
      color: "from-yellow-500 to-red-400",
      achievements: ["AI platform launch", "Autonomous navigation", "Industry recognition"]
    }
  ];

  return (
    <section id="timeline" ref={ref} className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-gray-900 to-black">
      <div className="max-w-6xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.h2 
            className="text-4xl md:text-6xl font-bold mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="bg-gradient-to-r from-red-500 to-red-300 bg-clip-text text-transparent">
              Our Journey
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            From a bold vision to industry leadership - discover the milestones that shaped SATINAV Robotics.
          </motion.p>
        </motion.div>

        {/* Timeline */}
        <div className="relative">
          {/* Timeline Line */}
          <motion.div
            initial={{ height: 0 }}
            animate={isInView ? { height: "100%" } : {}}
            transition={{ duration: 2, delay: 0.5 }}
            className="absolute left-1/2 transform -translate-x-1/2 w-1 bg-gradient-to-b from-red-500 to-red-300 hidden md:block"
          />

          {/* Timeline Items */}
          <div className="space-y-12 md:space-y-16">
            {milestones.map((milestone, index) => (
              <motion.div
                key={milestone.year}
                initial={{ opacity: 0, y: 50 }}
                animate={isInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.6, delay: 0.8 + index * 0.2 }}
                className={`flex flex-col md:flex-row items-center gap-8 ${
                  index % 2 === 0 ? 'md:flex-row' : 'md:flex-row-reverse'
                }`}
              >
                {/* Content */}
                <div className={`flex-1 ${index % 2 === 0 ? 'md:text-right' : 'md:text-left'}`}>
                  <motion.div
                    whileHover={{ scale: 1.02 }}
                    className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm border border-red-500/30 rounded-2xl p-6 hover:border-red-400/60 transition-all duration-300"
                  >
                    <div className="flex items-center gap-3 mb-4">
                      <motion.div
                        whileHover={{ rotate: 360 }}
                        transition={{ duration: 0.5 }}
                        className={`p-3 rounded-full bg-gradient-to-r ${milestone.color}`}
                      >
                        <milestone.icon className="w-6 h-6 text-white" />
                      </motion.div>
                      <div>
                        <h3 className="text-2xl font-bold text-white">{milestone.title}</h3>
                        <span className="text-red-400 font-semibold text-lg">{milestone.year}</span>
                      </div>
                    </div>
                    
                    <p className="text-gray-300 text-lg mb-4 leading-relaxed">
                      {milestone.description}
                    </p>

                    <div className="space-y-2">
                      {milestone.achievements.map((achievement, achievementIndex) => (
                        <motion.div
                          key={achievement}
                          initial={{ opacity: 0, x: index % 2 === 0 ? 20 : -20 }}
                          animate={isInView ? { opacity: 1, x: 0 } : {}}
                          transition={{ delay: 1 + index * 0.2 + achievementIndex * 0.1 }}
                          className="flex items-center gap-2 text-gray-400"
                        >
                          <div className="w-2 h-2 bg-red-400 rounded-full" />
                          <span>{achievement}</span>
                        </motion.div>
                      ))}
                    </div>
                  </motion.div>
                </div>

                {/* Timeline Node */}
                <motion.div
                  initial={{ scale: 0 }}
                  animate={isInView ? { scale: 1 } : {}}
                  transition={{ duration: 0.5, delay: 1 + index * 0.2 }}
                  whileHover={{ scale: 1.2 }}
                  className="relative z-10 w-16 h-16 bg-gradient-to-r from-red-600 to-red-400 rounded-full flex items-center justify-center shadow-lg shadow-red-500/25 border-4 border-black"
                >
                  <span className="text-white font-bold text-sm">{milestone.year}</span>
                  
                  {/* Pulse Animation */}
                  <motion.div
                    animate={{ scale: [1, 1.5, 1], opacity: [0.5, 0, 0.5] }}
                    transition={{ duration: 2, repeat: Infinity }}
                    className="absolute inset-0 bg-red-500 rounded-full"
                  />
                </motion.div>

                {/* Spacer for opposite side */}
                <div className="flex-1 hidden md:block" />
              </motion.div>
            ))}
          </div>
        </div>

        {/* Future Vision */}
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 2 }}
          className="mt-20 text-center"
        >
          <div className="bg-gradient-to-r from-red-500/10 to-red-300/10 backdrop-blur-sm border border-red-500/30 rounded-2xl p-8">
            <h3 className="text-3xl font-bold text-white mb-4">The Future Awaits</h3>
            <p className="text-xl text-gray-300 mb-6">
              We're just getting started. Join us as we continue to push the boundaries of robotics technology.
            </p>
            <motion.button
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(239, 68, 68, 0.5)" }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white font-bold py-3 px-8 rounded-full transition-all duration-300 shadow-lg hover:shadow-red-500/25"
            >
              Join Our Mission
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Timeline;
