# 🎨 SATINAV Landing Page Customization Guide

## 📁 File Structure for Your Content

```
public/
├── images/
│   ├── logos/
│   │   └── satinav-logo.png (or .svg)
│   ├── robots/
│   │   ├── hero-robot.jpg (main 3D robot replacement)
│   │   ├── modular-robot-1.jpg
│   │   ├── modular-robot-2.jpg
│   │   └── modular-robot-3.jpg
│   └── team/
│       ├── ceo-photo.jpg
│       ├── cto-photo.jpg
│       └── team-member-3.jpg
```

## 🔧 Text Content Locations

### 1. **Company Logo & Name**
**File:** `src/components/Navigation.tsx`
**Line 55:** Change "SATINAV" to your company name

To add your logo image:
```tsx
// Replace line 54-56 with:
<div className="flex items-center space-x-2">
  <img 
    src="/images/logos/your-logo.png" 
    alt="Your Company Logo" 
    className="w-8 h-8 object-contain"
  />
  <div className="text-2xl font-bold bg-gradient-to-r from-red-500 to-red-300 bg-clip-text text-transparent">
    YOUR COMPANY NAME
  </div>
</div>
```

### 2. **Hero Section (Main Landing)**
**File:** `src/components/SatinavHero.tsx`

**Lines 28-35:** Main headline
```tsx
<span className="bg-gradient-to-r from-white via-red-200 to-red-400 bg-clip-text text-transparent">
  Your Main {/* Change this */}
</span>
<br />
<span className="bg-gradient-to-r from-red-500 to-red-300 bg-clip-text text-transparent">
  Headline {/* Change this */}
</span>
```

**Lines 42-45:** Description text
```tsx
<motion.p className="text-xl md:text-2xl text-gray-300 mb-12 max-w-3xl leading-relaxed">
  Your company description and value proposition goes here. {/* Update this */}
  Make it compelling and clear about what you do.
</motion.p>
```

**Lines 60-75:** Feature pills - update these with your key features
```tsx
const features = [
  { icon: Zap, text: 'Your Feature 1' },
  { icon: Shield, text: 'Your Feature 2' },
  { icon: Cpu, text: 'Your Feature 3' },
  { icon: Rocket, text: 'Your Feature 4' }
];
```

### 3. **Robot Specifications**
**File:** `src/components/ModularRobots.tsx`
**Lines 12-60:** Complete robot data

```tsx
const robots = [
  {
    id: 'your-robot-1',
    name: 'Your Robot Name',
    description: 'Robot description',
    image: '/images/robots/your-robot-1.jpg', // Update image path
    specs: {
      'Specification 1': 'Value 1',
      'Specification 2': 'Value 2',
      'Specification 3': 'Value 3',
      'Specification 4': 'Value 4'
    },
    features: [
      'Feature 1 description',
      'Feature 2 description',
      'Feature 3 description'
    ]
  },
  // Add more robots...
];
```

### 4. **Company Timeline**
**File:** `src/components/Timeline.tsx`
**Lines 12-50:** Timeline milestones

```tsx
const milestones = [
  {
    year: '2020', // Your year
    title: 'Your Milestone Title',
    description: 'Description of what happened this year.',
    achievements: [
      'Achievement 1',
      'Achievement 2',
      'Achievement 3'
    ]
  },
  // Add more years...
];
```

### 5. **Team Members**
**File:** `src/components/Team.tsx`
**Lines 12-32:** Team data

```tsx
const teamMembers = [
  {
    name: "Your Team Member Name",
    role: "Their Role/Title",
    bio: "Brief bio about this person and their background.",
    skills: ["Skill 1", "Skill 2", "Skill 3"],
    social: { 
      linkedin: "https://linkedin.com/in/username",
      twitter: "https://twitter.com/username", 
      github: "https://github.com/username",
      email: "<EMAIL>"
    }
  },
  // Add more team members...
];
```

To add team photos, update the placeholder in the same file around line 70:
```tsx
// Replace the placeholder div with:
<img 
  src={`/images/team/${member.name.toLowerCase().replace(' ', '-')}.jpg`}
  alt={member.name}
  className="w-full h-full object-cover"
/>
```

### 6. **Contact Information**
**File:** `src/components/Contact.tsx`
**Lines 15-35:** Contact details

```tsx
const contactInfo = [
  {
    icon: MapPin,
    title: 'Visit Us',
    details: ['Your Street Address', 'City, State ZIP Code']
  },
  {
    icon: Phone,
    title: 'Call Us',
    details: ['+1 (XXX) XXX-XXXX', '+1 (XXX) XXX-XXXX']
  },
  {
    icon: Mail,
    title: 'Email Us',
    details: ['<EMAIL>', '<EMAIL>']
  }
];
```

### 7. **Page Metadata (SEO)**
**File:** `src/app/layout.tsx`
**Lines 8-12:** Update page title and description

```tsx
export const metadata: Metadata = {
  title: 'Your Company Name - Your Tagline',
  description: 'Your company description for search engines',
}
```

## 🖼️ Image Replacement Guide

### **Step 1:** Add your images to the public folder
- Logo: `public/images/logos/your-logo.png`
- Robot images: `public/images/robots/robot-1.jpg`, etc.
- Team photos: `public/images/team/person-name.jpg`

### **Step 2:** Update image paths in the code
- In robot data: change `image: '/images/robots/your-image.jpg'`
- In team section: add image elements as shown above
- In navigation: add logo image as shown above

### **Step 3:** 3D Robot Model (Optional)
The hero section uses a Spline 3D model. To replace it:
1. Create your 3D model in Spline (spline.design)
2. Export and get the embed URL
3. Update `src/components/SatinavHero.tsx` line 95:
```tsx
<Spline scene="YOUR_SPLINE_URL_HERE" />
```

## 🎨 Color Customization

The site uses a red/black theme. To change colors, update:
- `src/app/globals.css` - CSS custom properties
- Tailwind classes throughout components (search for `red-` to find all instances)

## 🚀 Quick Start Checklist

1. ✅ Add your logo to `public/images/logos/`
2. ✅ Add robot images to `public/images/robots/`
3. ✅ Add team photos to `public/images/team/`
4. ✅ Update company name in Navigation.tsx
5. ✅ Update hero headline and description in SatinavHero.tsx
6. ✅ Update robot specifications in ModularRobots.tsx
7. ✅ Update company timeline in Timeline.tsx
8. ✅ Update team member info in Team.tsx
9. ✅ Update contact information in Contact.tsx
10. ✅ Update page metadata in layout.tsx

After making changes, the development server will automatically reload and show your updates!
