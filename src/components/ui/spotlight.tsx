// src/components/ui/spotlight.tsx
'use client';

import React, { useRef, useState } from 'react';
import { motion, useSpring, useTransform, SpringOptions } from 'framer-motion';
import { cn } from '@/lib/utils';

type SpotlightProps = {
  className?: string;
  size?: number;
  fill?: string;
  springOptions?: SpringOptions;
};

/**
 * Renders a radial‐gradient “glow” that follows the mouse within its parent.
 * 
 * - The outer div LISTENS to onMouseMove; it must NOT have `pointer-events-none`.
 * - The inner glow circle HAS `pointer-events-none` so it doesn’t capture events itself.
 */
export function Spotlight({
  className,
  size = 200,
  fill = 'white',
  springOptions = { bounce: 0 },
}: SpotlightProps) {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isHovered, setIsHovered] = useState(false);

  // Create two springs for x/y, so the glow moves smoothly
  const springX = useSpring(0, springOptions);
  const springY = useSpring(0, springOptions);

  // Derive CSS left/top strings from the spring values
  const spotlightLeft = useTransform(springX, (x: number) => `${x - size / 2}px`);
  const spotlightTop = useTransform(springY, (y: number) => `${y - size / 2}px`);

  // Handler fires whenever the mouse moves inside this container
  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    const el = containerRef.current;
    if (!el) return;

    // Compute cursor position relative to the container’s top-left
    const rect = el.getBoundingClientRect();
    const relativeX = event.clientX - rect.left;
    const relativeY = event.clientY - rect.top;

    springX.set(relativeX);
    springY.set(relativeY);
  };

  return (
    <motion.div
      ref={containerRef}
      onMouseMove={handleMouseMove}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      // Remove pointer-events-none here so this div actually receives mouse events
      className={cn('absolute inset-0', className)}
    >
      {isHovered && (
        <motion.div
          // The glow circle itself should not intercept pointer events
          className="absolute rounded-full blur-3xl opacity-50 pointer-events-none"
          style={{
            width: size,
            height: size,
            left: spotlightLeft,
            top: spotlightTop,
            background: `radial-gradient(circle, ${fill} 0%, transparent 70%)`,
          }}
        />
      )}
    </motion.div>
  );
}

export default Spotlight;