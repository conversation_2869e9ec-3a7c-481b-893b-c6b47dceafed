'use client';

import { motion, useInView } from 'framer-motion';
import { useRef, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Zap, Shield, Cpu, Settings, Battery, Wifi } from 'lucide-react';

const ModularRobots = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });
  const [activeRobot, setActiveRobot] = useState(0);

  const robots = [
    {
      id: 1,
      name: "SATINAV-X1",
      category: "Reconnaissance",
      image: "/api/placeholder/400/300",
      specs: {
        speed: "45 km/h",
        range: "50 km",
        payload: "25 kg",
        battery: "8 hours"
      },
      features: [
        { icon: Zap, text: "High-Speed Mobility", color: "text-yellow-400" },
        { icon: Shield, text: "Stealth Mode", color: "text-blue-400" },
        { icon: Cpu, text: "AI Navigation", color: "text-green-400" }
      ],
      description: "Advanced reconnaissance robot designed for rapid deployment and intelligence gathering in challenging terrains."
    },
    {
      id: 2,
      name: "SATINAV-M2",
      category: "Heavy Duty",
      image: "/api/placeholder/400/300",
      specs: {
        speed: "25 km/h",
        range: "80 km",
        payload: "150 kg",
        battery: "12 hours"
      },
      features: [
        { icon: Settings, text: "Modular Design", color: "text-purple-400" },
        { icon: Battery, text: "Extended Battery", color: "text-green-400" },
        { icon: Shield, text: "Armor Plating", color: "text-red-400" }
      ],
      description: "Heavy-duty modular robot built for construction, logistics, and industrial applications with maximum payload capacity."
    },
    {
      id: 3,
      name: "SATINAV-S3",
      category: "Surveillance",
      image: "/api/placeholder/400/300",
      specs: {
        speed: "35 km/h",
        range: "60 km",
        payload: "40 kg",
        battery: "16 hours"
      },
      features: [
        { icon: Wifi, text: "5G Connectivity", color: "text-cyan-400" },
        { icon: Cpu, text: "Edge Computing", color: "text-orange-400" },
        { icon: Shield, text: "Weather Resistant", color: "text-blue-400" }
      ],
      description: "Surveillance specialist with advanced sensors and long-range communication capabilities for security operations."
    }
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  };

  return (
    <section id="robots" ref={ref} className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.h2 
            className="text-4xl md:text-6xl font-bold mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="bg-gradient-to-r from-red-500 to-red-300 bg-clip-text text-transparent">
              Modular Ground Robots
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Discover our cutting-edge fleet of modular robots, each designed for specific missions 
            with unparalleled adaptability and performance.
          </motion.p>
        </motion.div>

        {/* Robot Selector */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="flex justify-center mb-12"
        >
          <div className="flex bg-black/50 backdrop-blur-sm rounded-full p-2 border border-red-500/30">
            {robots.map((robot, index) => (
              <motion.button
                key={robot.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setActiveRobot(index)}
                className={`px-6 py-3 rounded-full font-medium transition-all duration-300 ${
                  activeRobot === index
                    ? 'bg-red-500 text-white shadow-lg shadow-red-500/25'
                    : 'text-gray-300 hover:text-white hover:bg-white/10'
                }`}
              >
                {robot.name}
              </motion.button>
            ))}
          </div>
        </motion.div>

        {/* Active Robot Display */}
        <motion.div
          key={activeRobot}
          initial={{ opacity: 0, x: 50 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="grid lg:grid-cols-2 gap-12 items-center"
        >
          {/* Robot Image */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            className="relative"
          >
            <Card className="bg-gradient-to-br from-gray-900 to-black border-red-500/30 overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-red-900/20 to-black flex items-center justify-center">
                <div className="text-6xl text-red-500/50">🤖</div>
              </div>
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
              <div className="absolute bottom-4 left-4">
                <span className="bg-red-500 text-white px-3 py-1 rounded-full text-sm font-medium">
                  {robots[activeRobot].category}
                </span>
              </div>
            </Card>
          </motion.div>

          {/* Robot Details */}
          <div className="space-y-8">
            <div>
              <motion.h3
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="text-3xl font-bold text-white mb-4"
              >
                {robots[activeRobot].name}
              </motion.h3>
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="text-gray-300 text-lg leading-relaxed"
              >
                {robots[activeRobot].description}
              </motion.p>
            </div>

            {/* Specifications */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="grid grid-cols-2 gap-4"
            >
              {Object.entries(robots[activeRobot].specs).map(([key, value], index) => (
                <motion.div
                  key={key}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.3 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  className="bg-black/50 backdrop-blur-sm border border-red-500/30 rounded-lg p-4 hover:border-red-400/60 transition-all duration-300"
                >
                  <div className="text-red-400 font-medium capitalize">{key}</div>
                  <div className="text-white text-xl font-bold">{value}</div>
                </motion.div>
              ))}
            </motion.div>

            {/* Features */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="space-y-3"
            >
              <h4 className="text-xl font-semibold text-white">Key Features</h4>
              <div className="space-y-2">
                {robots[activeRobot].features.map((feature, index) => (
                  <motion.div
                    key={feature.text}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    whileHover={{ x: 10 }}
                    className="flex items-center gap-3 text-gray-300 hover:text-white transition-colors duration-200"
                  >
                    <feature.icon className={`w-5 h-5 ${feature.color}`} />
                    <span>{feature.text}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* CTA Button */}
            <motion.button
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(239, 68, 68, 0.5)" }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white font-bold py-3 px-8 rounded-full transition-all duration-300 shadow-lg hover:shadow-red-500/25"
            >
              Learn More
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default ModularRobots;
