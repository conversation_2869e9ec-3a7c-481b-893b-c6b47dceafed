import type { NextConfig } from 'next'
import path from 'path'

const nextConfig: NextConfig = {
  // keep your existing alias
  webpack(config) {
    config.resolve.alias = {
      ...config.resolve.alias,
      '@splinetool/react-spline$': path.resolve(
        __dirname,
        'node_modules/@splinetool/react-spline/dist/react-spline.js'
      )
    }
    return config
  },

  // ADD THIS BLOCK:
  eslint: {
    // Skip ESLint checks (and errors) during production builds
    ignoreDuringBuilds: true
  }
}

export default nextConfig