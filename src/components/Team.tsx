'use client';

import { motion, useInView } from 'framer-motion';
import { useRef } from 'react';
import { Card } from '@/components/ui/card';
import { Linkedin, Twitter, Github, Mail } from 'lucide-react';

const Team = () => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, margin: "-100px" });

  const teamMembers = [
    {
      name: "Dr. <PERSON>",
      role: "CEO & Co-Founder",
      bio: "Former MIT robotics professor with 15+ years in autonomous systems.",
      skills: ["Robotics", "AI", "Leadership"],
      social: { linkedin: "#", twitter: "#", email: "<EMAIL>" }
    },
    {
      name: "<PERSON>", 
      role: "<PERSON><PERSON> & Co-Founder",
      bio: "Ex-Boston Dynamics engineer specializing in mechanical design.",
      skills: ["Mechanical Design", "Control Systems", "Innovation"],
      social: { linkedin: "#", github: "#", email: "<EMAIL>" }
    },
    {
      name: "Dr. <PERSON><PERSON>",
      role: "Head of AI", 
      bio: "PhD in Machine Learning from Stanford. Expert in computer vision.",
      skills: ["Machine Learning", "Computer Vision", "Algorithms"],
      social: { linkedin: "#", twitter: "#", github: "#" }
    }
  ];

  return (
    <section id="team" ref={ref} className="py-20 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-black to-gray-900">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.h2 
            className="text-4xl md:text-6xl font-bold mb-6"
            initial={{ opacity: 0, scale: 0.8 }}
            animate={isInView ? { opacity: 1, scale: 1 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
          >
            <span className="bg-gradient-to-r from-red-500 to-red-300 bg-clip-text text-transparent">
              Meet Our Team
            </span>
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            World-class experts united by a shared vision to revolutionize robotics technology.
          </motion.p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 50 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: 0.6 + index * 0.1 }}
              whileHover={{ y: -10 }}
              className="group"
            >
              <Card className="bg-gradient-to-br from-gray-900/80 to-black/80 backdrop-blur-sm border-red-500/30 hover:border-red-400/60 transition-all duration-300 overflow-hidden">
                <div className="relative overflow-hidden">
                  <div className="aspect-square bg-gradient-to-br from-red-900/20 to-black flex items-center justify-center">
                    <div className="text-6xl text-red-500/50">👤</div>
                  </div>
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                  
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    whileHover={{ opacity: 1, y: 0 }}
                    className="absolute bottom-4 left-4 right-4 flex justify-center gap-3 opacity-0 group-hover:opacity-100 transition-all duration-300"
                  >
                    {member.social.linkedin && (
                      <motion.a
                        whileHover={{ scale: 1.2 }}
                        whileTap={{ scale: 0.9 }}
                        href={member.social.linkedin}
                        className="p-2 bg-blue-600 rounded-full hover:bg-blue-500 transition-colors"
                      >
                        <Linkedin className="w-4 h-4 text-white" />
                      </motion.a>
                    )}
                    {member.social.twitter && (
                      <motion.a
                        whileHover={{ scale: 1.2 }}
                        whileTap={{ scale: 0.9 }}
                        href={member.social.twitter}
                        className="p-2 bg-sky-500 rounded-full hover:bg-sky-400 transition-colors"
                      >
                        <Twitter className="w-4 h-4 text-white" />
                      </motion.a>
                    )}
                    {member.social.github && (
                      <motion.a
                        whileHover={{ scale: 1.2 }}
                        whileTap={{ scale: 0.9 }}
                        href={member.social.github}
                        className="p-2 bg-gray-700 rounded-full hover:bg-gray-600 transition-colors"
                      >
                        <Github className="w-4 h-4 text-white" />
                      </motion.a>
                    )}
                    {member.social.email && (
                      <motion.a
                        whileHover={{ scale: 1.2 }}
                        whileTap={{ scale: 0.9 }}
                        href={`mailto:${member.social.email}`}
                        className="p-2 bg-red-600 rounded-full hover:bg-red-500 transition-colors"
                      >
                        <Mail className="w-4 h-4 text-white" />
                      </motion.a>
                    )}
                  </motion.div>
                </div>

                <div className="p-6">
                  <h3 className="text-xl font-bold text-white mb-2">{member.name}</h3>
                  <p className="text-red-400 font-semibold mb-3">{member.role}</p>
                  <p className="text-gray-300 text-sm leading-relaxed mb-4">{member.bio}</p>
                  <div className="flex flex-wrap gap-2">
                    {member.skills.map((skill) => (
                      <span
                        key={skill}
                        className="bg-red-500/20 text-red-300 px-2 py-1 rounded-full text-xs font-medium border border-red-500/30"
                      >
                        {skill}
                      </span>
                    ))}
                  </div>
                </div>
              </Card>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={isInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, delay: 1 }}
          className="mt-16 text-center"
        >
          <div className="bg-gradient-to-r from-red-500/10 to-red-300/10 backdrop-blur-sm border border-red-500/30 rounded-2xl p-8">
            <h3 className="text-3xl font-bold text-white mb-4">Join Our Mission</h3>
            <p className="text-xl text-gray-300 mb-6 max-w-2xl mx-auto">
              We're always looking for talented individuals who share our passion for robotics innovation.
            </p>
            <motion.button
              whileHover={{ scale: 1.05, boxShadow: "0 0 30px rgba(239, 68, 68, 0.5)" }}
              whileTap={{ scale: 0.95 }}
              className="bg-gradient-to-r from-red-600 to-red-500 hover:from-red-500 hover:to-red-400 text-white font-bold py-3 px-8 rounded-full transition-all duration-300 shadow-lg hover:shadow-red-500/25"
            >
              View Open Positions
            </motion.button>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Team;
