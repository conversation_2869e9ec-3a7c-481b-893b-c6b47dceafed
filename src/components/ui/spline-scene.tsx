// src/components/ui/spline-scene.tsx
'use client';

import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';

// Framer Motion imports (used by Spotlight, but keep them here if you need motions here too)
import { motion, useSpring, useTransform, SpringOptions } from 'framer-motion';
import { cn } from '@/lib/utils';

interface SplineSceneProps {
  scene: string;
  className?: string;
}

// ❗️ Use Next.js dynamic import so that @splinetool/react-spline only loads on the client.
//    Without `ssr: false`, Next.js will try to bundle it on the server and fail.
const Spline = dynamic(
  () =>
    import('@splinetool/react-spline').then((mod) => mod.default),
  { ssr: false }
);

export function SplineScene({ scene, className }: SplineSceneProps) {
  return (
    <div className={className}>
      <Suspense
        fallback={
          <div className="w-full h-full flex items-center justify-center">
            {/* You can replace this with a spinner component */}
            <span className="loader"></span>
          </div>
        }
      >
        <Spline scene={scene} />
      </Suspense>
    </div>
  );
}