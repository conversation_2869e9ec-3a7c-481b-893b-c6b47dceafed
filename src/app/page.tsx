// src/app/page.tsx
'use client';

import Navigation from '@/components/Navigation';
import SatinavHero from '@/components/SatinavHero';
import ModularRobots from '@/components/ModularRobots';
import Timeline from '@/components/Timeline';
import Team from '@/components/Team';
import Contact from '@/components/Contact';
import FloatingActionButton from '@/components/ui/floating-action-button';
import Particles from '@/components/ui/particles';

export default function Home() {
  return (
    <div className="min-h-screen bg-black text-white relative overflow-hidden">
      {/* Background Particles */}
      <Particles count={30} className="fixed inset-0 z-0" />

      <div className="relative z-10">
        <Navigation />
        <main>
          <SatinavHero />
          <ModularRobots />
          <Timeline />
          <Team />
          <Contact />
        </main>
      </div>

      {/* Floating Action Button */}
      <FloatingActionButton />
    </div>
  );
}