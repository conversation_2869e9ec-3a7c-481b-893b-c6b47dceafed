'use client';

import { useRef, Suspense } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { OrbitControls } from '@react-three/drei';
import * as THREE from 'three';

// Robot component with procedural geometry
function Robot() {
  const robotRef = useRef<THREE.Group>(null);
  const headRef = useRef<THREE.Mesh>(null);
  const leftArmRef = useRef<THREE.Group>(null);
  const rightArmRef = useRef<THREE.Group>(null);
  const antennaRef = useRef<THREE.Mesh>(null);

  useFrame((state) => {
    if (!robotRef.current) return;

    // Main robot floating animation
    robotRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.2;
    robotRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 0.3) * 0.1;

    // Head rotation
    if (headRef.current) {
      headRef.current.rotation.y = Math.sin(state.clock.elapsedTime * 1.2) * 0.3;
    }

    // Arm movements
    if (leftArmRef.current) {
      leftArmRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 1.5) * 0.2 + 0.3;
    }
    if (rightArmRef.current) {
      rightArmRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 1.5 + Math.PI) * 0.2 - 0.3;
    }

    // Antenna animation
    if (antennaRef.current) {
      antennaRef.current.rotation.z = Math.sin(state.clock.elapsedTime * 2) * 0.1;
    }
  });

  // Materials
  const bodyMaterial = new THREE.MeshStandardMaterial({ 
    color: '#1a1a1a',
    metalness: 0.8,
    roughness: 0.2
  });
  
  const accentMaterial = new THREE.MeshStandardMaterial({ 
    color: '#ef4444',
    metalness: 0.9,
    roughness: 0.1,
    emissive: '#ef4444',
    emissiveIntensity: 0.2
  });

  const eyeMaterial = new THREE.MeshStandardMaterial({ 
    color: '#00ffff',
    emissive: '#00ffff',
    emissiveIntensity: 0.8
  });

  return (
    <group ref={robotRef} position={[0, 0, 0]}>
      {/* Body */}
      <mesh position={[0, 0, 0]}>
        <boxGeometry args={[1.2, 1.5, 0.8]} />
        <primitive object={bodyMaterial} />
      </mesh>

      {/* Chest panel */}
      <mesh position={[0, 0.2, 0.41]}>
        <boxGeometry args={[0.8, 0.6, 0.02]} />
        <primitive object={accentMaterial} />
      </mesh>

      {/* Head */}
      <group ref={headRef} position={[0, 1.2, 0]}>
        <mesh>
          <boxGeometry args={[0.8, 0.8, 0.8]} />
          <primitive object={bodyMaterial} />
        </mesh>
        
        {/* Eyes */}
        <mesh position={[-0.2, 0.1, 0.41]}>
          <sphereGeometry args={[0.08]} />
          <primitive object={eyeMaterial} />
        </mesh>
        <mesh position={[0.2, 0.1, 0.41]}>
          <sphereGeometry args={[0.08]} />
          <primitive object={eyeMaterial} />
        </mesh>

        {/* Antenna */}
        <mesh ref={antennaRef} position={[0, 0.5, 0]}>
          <cylinderGeometry args={[0.02, 0.02, 0.4]} />
          <primitive object={accentMaterial} />
        </mesh>
        <mesh position={[0, 0.9, 0]}>
          <sphereGeometry args={[0.05]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Left Arm */}
      <group ref={leftArmRef} position={[-0.8, 0.5, 0]}>
        <mesh position={[0, -0.3, 0]}>
          <cylinderGeometry args={[0.15, 0.15, 0.8]} />
          <primitive object={bodyMaterial} />
        </mesh>
        <mesh position={[0, -0.8, 0]}>
          <sphereGeometry args={[0.2]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Right Arm */}
      <group ref={rightArmRef} position={[0.8, 0.5, 0]}>
        <mesh position={[0, -0.3, 0]}>
          <cylinderGeometry args={[0.15, 0.15, 0.8]} />
          <primitive object={bodyMaterial} />
        </mesh>
        <mesh position={[0, -0.8, 0]}>
          <sphereGeometry args={[0.2]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Legs */}
      <group position={[0, -1.2, 0]}>
        <mesh position={[-0.3, -0.4, 0]}>
          <cylinderGeometry args={[0.18, 0.18, 0.8]} />
          <primitive object={bodyMaterial} />
        </mesh>
        <mesh position={[0.3, -0.4, 0]}>
          <cylinderGeometry args={[0.18, 0.18, 0.8]} />
          <primitive object={bodyMaterial} />
        </mesh>
        
        {/* Feet */}
        <mesh position={[-0.3, -0.9, 0.2]}>
          <boxGeometry args={[0.3, 0.1, 0.5]} />
          <primitive object={accentMaterial} />
        </mesh>
        <mesh position={[0.3, -0.9, 0.2]}>
          <boxGeometry args={[0.3, 0.1, 0.5]} />
          <primitive object={accentMaterial} />
        </mesh>
      </group>

      {/* Floating particles around robot */}
      <group>
        {Array.from({ length: 8 }).map((_, i) => (
          <mesh
            key={i}
            position={[
              Math.cos((i / 8) * Math.PI * 2) * 2,
              Math.sin((i / 8) * Math.PI * 2) * 0.5,
              Math.sin((i / 8) * Math.PI * 2) * 2
            ]}
          >
            <sphereGeometry args={[0.02]} />
            <meshStandardMaterial
              color="#ef4444"
              emissive="#ef4444"
              emissiveIntensity={0.5}
            />
          </mesh>
        ))}
      </group>
    </group>
  );
}

// Lighting setup
function Lights() {
  return (
    <>
      <ambientLight intensity={0.4} />
      <directionalLight
        position={[10, 10, 5]}
        intensity={1}
        castShadow
        shadow-mapSize-width={2048}
        shadow-mapSize-height={2048}
      />
      <pointLight position={[-10, -10, -10]} intensity={0.5} color="#ef4444" />
      <spotLight
        position={[0, 10, 0]}
        angle={0.3}
        penumbra={1}
        intensity={0.8}
        color="#ffffff"
      />
    </>
  );
}

// Main Robot3D component
export default function Robot3D() {
  return (
    <div className="w-full h-full">
      <Suspense fallback={<div className="flex items-center justify-center h-full text-red-400">Loading Robot...</div>}>
        <Canvas
          camera={{ position: [0, 0, 8], fov: 50 }}
          gl={{ antialias: true, alpha: true }}
          style={{ background: 'transparent' }}
        >
          <Lights />
          <Robot />
          <OrbitControls
            enableZoom={false}
            enablePan={false}
            autoRotate
            autoRotateSpeed={0.5}
            maxPolarAngle={Math.PI / 2}
            minPolarAngle={Math.PI / 3}
          />
        </Canvas>
      </Suspense>
    </div>
  );
}
